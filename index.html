<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARK</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sky-950': '#0c4a6e',
                        'orange-400': '#fb923c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <img class="h-14 w-auto" src="assets/images/logo_ark.svg" alt="ARK Logo">
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-orange-400 hover:text-orange-500 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-orange-400">
                        Home
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Car Rental
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Taxi Services
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Services
                    </a>
                </nav>

                <!-- Language Select -->
                <div class="relative">
                    <button class="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-300 cursor-pointer flex items-center" id="language-button">
                        <span id="selected-language">EN</span>
                        <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <ul class="absolute right-0 mt-2 w-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 hidden" id="language-dropdown">
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="en">EN</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="fr">FR</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="de">DE</a>
                        </li>
                    </ul>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-sky-950 hover:text-orange-400 focus:outline-none focus:text-orange-400" id="mobile-menu-button">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="#" class="text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-orange-400">Home</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">About</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Contact</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Services</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gray-50 py-16 lg:py-20" style="background-color: #F9FAFB;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                <!-- Left Content -->
                <div class="text-left order-2 lg:order-1">
                    <h1 class="text-xl lg:text-4xl xl:text-5xl font-bold text-sky-950 leading-tight mb-6">
                        Your Journey<br>
                        Your Choice
                    </h1>
                    <p class="text-base lg:text-lg text-gray-600 mb-8 leading-relaxed">
                        Rent a Car or Book a Taxi - All in One Place
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Rent a Car
                        </button>
                        <button class="bg-sky-950 hover:bg-sky-900 text-white px-6 py-3 rounded-lg text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Book a Taxi
                        </button>
                    </div>
                </div>

                <!-- Right Content - Car Image -->
                <div class="flex justify-center lg:justify-end order-1 lg:order-2">
                    <img src="assets/images/hero_car.png" alt="Car Rental" class="w-full max-w-lg lg:max-w-xl xl:max-w-2xl h-auto object-contain">
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="bg-white py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-orange-400 mb-4">
                    Why Choose Us
                </h2>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
                <!-- 24/7 Customer Support -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/headphone.svg" alt="24/7 Customer Support" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        24/7 Customer Support
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Always here to help
                    </p>
                </div>

                <!-- No Hidden Charges -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/shield.svg" alt="No Hidden Charges" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        No Hidden Charges
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Transparent pricing
                    </p>
                </div>

                <!-- Secure Payments -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/card.svg" alt="Secure Payments" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        Secure Payments
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Safe & encrypted
                    </p>
                </div>

                <!-- Free Cancellation -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/customer.svg" alt="Free Cancellation" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        Free Cancellation
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Flexible booking
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Featured Vehicles Section -->
    <section class="bg-gray-50 py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-4">
                    Our Featured Vehicles
                </h2>
            </div>

            <!-- Vehicles Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Mercedes-Benz E-Class -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_white.jpg" alt="Mercedes-Benz E-Class" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">Perodua Bezza White</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$89</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Hybrid" class="w-4 h-4">
                                <span>Hybrid</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>

                <!-- BMW 5 Series -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_brown.jpg" alt="BMW 5 Series" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">BMW 5 Series</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$79</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Petrol" class="w-4 h-4">
                                <span>Petrol</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>

                <!-- Tesla Model 3 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_silver.jpg" alt="Tesla Model 3" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">Tesla Model 3</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$99</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Electric" class="w-4 h-4">
                                <span>Electric</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="bg-white py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">

                <!-- Car Rental Services -->
                <div>
                    <h2 class="text-2xl lg:text-3xl font-bold text-sky-950 mb-8">
                        Car Rental Services
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Wide Vehicle Selection -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/wide.svg" alt="Wide Vehicle Selection" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Wide Vehicle Selection</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- 24/7 Customer Support -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/24_7_customer.svg" alt="24/7 Customer Support" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">24/7 Customer Support</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Flexible Pickup/Drop-off -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/flexible.svg" alt="Flexible Pickup/Drop-off" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Flexible Pickup/Drop-off</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Insurance Included -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/insurance.svg" alt="Insurance Included" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Insurance Included</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Taxi Services -->
                <div>
                    <h2 class="text-2xl lg:text-3xl font-bold text-sky-950 mb-8">
                        Taxi Services
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Professional Drivers -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/professional.svg" alt="Professional Drivers" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Professional Drivers</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Airport Transfers -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/airport.svg" alt="Airport Transfers" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Airport Transfers</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Fixed Rates -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/fix_rate.svg" alt="Fixed Rates" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Fixed Rates</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Corporate Accounts -->
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0 mt-1">
                                <img src="assets/images/corporate.svg" alt="Corporate Accounts" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Corporate Accounts</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Book a Taxi Section -->
    <section class="bg-gray-50 py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">

                <!-- Left Content -->
                <div class="order-2 lg:order-1">
                    <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-6">
                        Book a Taxi
                    </h2>

                    <p class="text-gray-600 text-lg mb-8 leading-relaxed">
                        Experience reliable and comfortable taxi services with our professional drivers.
                        Book your ride today and enjoy hassle-free transportation.
                    </p>

                    <!-- Features List -->
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center gap-3">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0">
                            <span class="text-gray-700">Professional and experienced drivers</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0">
                            <span class="text-gray-700">24/7 availability for your convenience</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0">
                            <span class="text-gray-700">Competitive and transparent pricing</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0">
                            <span class="text-gray-700">Clean and well-maintained vehicles</span>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <button class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                        Book Now
                    </button>

                </div>

                <!-- Right Image -->
                <div class="order-1 lg:order-2">
                    <div class="relative">
                        <img src="assets/images/taxi_car.png" alt="Taxi Car" class="w-full h-auto rounded-lg">
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- What Our Customers Say Section -->
    <section class="bg-white py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-4">
                    What Our Customers Say
                </h2>
            </div>

            <!-- Testimonials Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

                <!-- Testimonial 1 - Sarah Johnson -->
                <div class="bg-gray-50 rounded-lg p-6 shadow-sm">
                    <!-- Customer Info -->
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            SJ
                        </div>
                        <div>
                            <h3 class="font-semibold text-sky-950">Sarah Johnson</h3>
                            <!-- Star Rating -->
                            <div class="flex gap-1 mt-1">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial Text -->
                    <p class="text-gray-600 leading-relaxed">
                        Excellent service! The car was in perfect condition and the staff was very helpful.
                    </p>
                </div>

                <!-- Testimonial 2 - Michael Chen -->
                <div class="bg-gray-50 rounded-lg p-6 shadow-sm">
                    <!-- Customer Info -->
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            MC
                        </div>
                        <div>
                            <h3 class="font-semibold text-sky-950">Michael Chen</h3>
                            <!-- Star Rating -->
                            <div class="flex gap-1 mt-1">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial Text -->
                    <p class="text-gray-600 leading-relaxed">
                        Very professional and reliable. Will definitely use their services again.
                    </p>
                </div>

                <!-- Testimonial 3 - Emily Davis -->
                <div class="bg-gray-50 rounded-lg p-6 shadow-sm">
                    <!-- Customer Info -->
                    <div class="flex items-center gap-4 mb-4">
                        <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            ED
                        </div>
                        <div>
                            <h3 class="font-semibold text-sky-950">Emily Davis</h3>
                            <!-- Star Rating -->
                            <div class="flex gap-1 mt-1">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                            </div>
                        </div>
                    </div>
                    <!-- Testimonial Text -->
                    <p class="text-gray-600 leading-relaxed">
                        Great experience from booking to drop-off. Highly recommended!
                    </p>
                </div>

            </div>
        </div>
    </section>

    <!-- Contact Us Section -->
    <section class="bg-gray-50 py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">

                <!-- Left Side - Contact Form -->
                <div>
                    <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-8">
                        Contact Us
                    </h2>

                    <!-- Contact Form -->
                    <form class="space-y-6">
                        <!-- Name Field -->
                        <div>
                            <label for="name" class="block text-orange-400 font-medium mb-2">Name</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                placeholder="Your name"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors"
                            >
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-orange-400 font-medium mb-2">Email</label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                placeholder="Your email"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors"
                            >
                        </div>

                        <!-- Message Field -->
                        <div>
                            <label for="message" class="block text-orange-400 font-medium mb-2">Message</label>
                            <textarea
                                id="message"
                                name="message"
                                rows="5"
                                placeholder="Your message"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors resize-vertical"
                            ></textarea>
                        </div>

                        <!-- Submit Button -->
                        <button
                            type="submit"
                            class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200"
                        >
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Right Side - Contact Information -->
                <div>
                    <h3 class="text-2xl font-bold text-sky-950 mb-8">
                        Contact Information
                    </h3>

                    <!-- Contact Details -->
                    <div class="space-y-6 mb-8">
                        <!-- Address -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_pin.svg" alt="Location" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">123 Transportation Ave, City, Country</span>
                        </div>

                        <!-- Phone -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_phone.svg" alt="Phone" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">+1 ***********</span>
                        </div>

                        <!-- Email -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_email.svg" alt="Email" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700"><EMAIL></span>
                        </div>

                        <!-- Hours -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_time.png" alt="Hours" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">24/7 Available</span>
                        </div>
                    </div>

                    <!-- Map -->
                    <div class="rounded-lg overflow-hidden">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.1234567890123!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a316e5b7c5d%3A0x1234567890abcdef!2s123%20Transportation%20Ave%2C%20New%20York%2C%20NY%2010001%2C%20USA!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus"
                            width="100%"
                            height="256"
                            style="border:0;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"
                            class="w-full h-64"
                        ></iframe>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-12 border-t border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <div class="flex items-center gap-3 mb-4">
                        <img src="assets/images/logo_ark.svg" alt="ARK Logo" class="h-14 w-auto">
                    </div>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Your trusted partner in transportation services.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">About Us</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Our Fleet</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Taxi Service</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Contact</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Contact</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <img src="assets/images/footer_phone.svg" alt="Phone" class="w-4 h-4 flex-shrink-0">
                            <span class="text-gray-600 text-sm">+1 ***********</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="assets/images/footer_email.png" alt="Email" class="w-4 h-4 flex-shrink-0">
                            <span class="text-gray-600 text-sm"><EMAIL></span>
                        </div>
                    </div>
                </div>

                <!-- Follow Us -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Follow Us</h3>
                    <div class="flex gap-3">
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/facebook.svg" alt="Facebook" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/twitter.svg" alt="Twitter" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/instamgram.svg" alt="Instagram" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/linkedin.svg" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

            </div>

            <!-- Footer Bottom -->
            <div class="border-t border-gray-200 mt-8 pt-6">
                <div class="text-center">
                    <p class="text-sky-950 text-sm">
                        © 2024 Ark Car Rental. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>



    <!-- JavaScript for mobile menu toggle and language dropdown -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language dropdown functionality
        const languageButton = document.getElementById('language-button');
        const languageDropdown = document.getElementById('language-dropdown');
        const selectedLanguage = document.getElementById('selected-language');

        // Toggle dropdown
        languageButton.addEventListener('click', function(e) {
            e.preventDefault();
            languageDropdown.classList.toggle('hidden');
        });

        // Handle language selection
        const languageOptions = languageDropdown.querySelectorAll('a[data-lang]');
        languageOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const selectedLang = this.getAttribute('data-lang').toUpperCase();
                selectedLanguage.textContent = selectedLang;
                languageDropdown.classList.add('hidden');

                // You can add your language switching logic here
                console.log('Language changed to:', selectedLang);
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!languageButton.contains(e.target) && !languageDropdown.contains(e.target)) {
                languageDropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>