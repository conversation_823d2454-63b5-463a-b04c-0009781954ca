<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARK</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sky-950': '#0c4a6e',
                        'orange-400': '#fb923c'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <img class="h-14 w-auto" src="assets/images/logo_ark.svg" alt="ARK Logo">
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-orange-400 hover:text-orange-500 px-3 py-2 text-sm font-medium transition-colors duration-200 border-b-2 border-orange-400">
                        Home
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-sm font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        About
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-sm font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Contact
                    </a>
                    <a href="#" class="text-sky-950 hover:text-orange-400 px-3 py-2 text-sm font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Services
                    </a>
                </nav>

                <!-- Language Select -->
                <div class="relative">
                    <button class="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-300 cursor-pointer flex items-center" id="language-button">
                        <span id="selected-language">EN</span>
                        <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <ul class="absolute right-0 mt-2 w-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 hidden" id="language-dropdown">
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="en">EN</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="fr">FR</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="de">DE</a>
                        </li>
                    </ul>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-sky-950 hover:text-orange-400 focus:outline-none focus:text-orange-400" id="mobile-menu-button">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="#" class="text-orange-400 block px-3 py-2 text-base font-medium border-l-4 border-orange-400">Home</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-base font-medium border-l-4 border-transparent hover:border-orange-400">About</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-base font-medium border-l-4 border-transparent hover:border-orange-400">Contact</a>
                <a href="#" class="text-sky-950 hover:text-orange-400 block px-3 py-2 text-base font-medium border-l-4 border-transparent hover:border-orange-400">Services</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gray-50 py-16 lg:py-20" style="background-color: #F9FAFB;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                <!-- Left Content -->
                <div class="text-left">
                    <h1 class="text-xl lg:text-4xl xl:text-5xl font-bold text-sky-950 leading-tight mb-6">
                        Your Journey<br>
                        Your Choice
                    </h1>
                    <p class="text-base lg:text-lg text-gray-600 mb-8 leading-relaxed">
                        Rent a Car or Book a Taxi - All in One Place
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Rent a Car
                        </button>
                        <button class="bg-sky-950 hover:bg-sky-900 text-white px-6 py-3 rounded-lg text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl">
                            Book a Taxi
                        </button>
                    </div>
                </div>

                <!-- Right Content - Car Image -->
                <div class="flex justify-center lg:justify-end">
                    <img src="assets/images/hero_car.png" alt="Car Rental" class="w-full max-w-lg lg:max-w-xl xl:max-w-2xl h-auto object-contain">
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="bg-white py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-orange-400 mb-4">
                    Why Choose Us
                </h2>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
                <!-- 24/7 Customer Support -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/headphone.svg" alt="24/7 Customer Support" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-sky-950 mb-3">
                        24/7 Customer Support
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Always here to help
                    </p>
                </div>

                <!-- No Hidden Charges -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/shield.svg" alt="No Hidden Charges" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-sky-950 mb-3">
                        No Hidden Charges
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Transparent pricing
                    </p>
                </div>

                <!-- Secure Payments -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/card.svg" alt="Secure Payments" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-sky-950 mb-3">
                        Secure Payments
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Safe & encrypted
                    </p>
                </div>

                <!-- Free Cancellation -->
                <div class="text-center">
                    <div class="flex justify-center mb-6">
                        <img src="assets/images/customer.svg" alt="Free Cancellation" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-sky-950 mb-3">
                        Free Cancellation
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Flexible booking
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content Area -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
                <p class="text-gray-500 text-lg">Your content goes here</p>

                
            </div>
        </div>
    </main>

    <!-- JavaScript for mobile menu toggle and language dropdown -->
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language dropdown functionality
        const languageButton = document.getElementById('language-button');
        const languageDropdown = document.getElementById('language-dropdown');
        const selectedLanguage = document.getElementById('selected-language');

        // Toggle dropdown
        languageButton.addEventListener('click', function(e) {
            e.preventDefault();
            languageDropdown.classList.toggle('hidden');
        });

        // Handle language selection
        const languageOptions = languageDropdown.querySelectorAll('a[data-lang]');
        languageOptions.forEach(option => {
            option.addEventListener('click', function(e) {
                e.preventDefault();
                const selectedLang = this.getAttribute('data-lang').toUpperCase();
                selectedLanguage.textContent = selectedLang;
                languageDropdown.classList.add('hidden');

                // You can add your language switching logic here
                console.log('Language changed to:', selectedLang);
            });
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!languageButton.contains(e.target) && !languageDropdown.contains(e.target)) {
                languageDropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>