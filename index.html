<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ARK</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sky-950': '#0c4a6e',
                        'orange-400': '#fb923c'
                    }
                }
            }
        }
    </script>
    <style>
        /* Hero Section Animations */
        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .animate-fade-in-left {
            animation: fadeInLeft 1s ease-out;
        }

        .animate-fade-in-right {
            animation: fadeInRight 1s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.8s ease-out;
        }

        .animate-slide-up-delay-1 {
            animation: slideUp 0.8s ease-out 0.2s both;
        }

        .animate-slide-up-delay-2 {
            animation: slideUp 0.8s ease-out 0.4s both;
        }

        .animate-float {
            animation: float 3s ease-in-out infinite;
        }

        /* Why Choose Us Section Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounceGentle {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-5px);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        .animate-fade-in-up-delay-1 {
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .animate-fade-in-up-delay-2 {
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .animate-fade-in-up-delay-3 {
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .animate-fade-in-up-delay-4 {
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .animate-bounce-gentle {
            animation: bounceGentle 2s ease-in-out infinite;
        }

        .animate-bounce-gentle-delay-1 {
            animation: bounceGentle 2s ease-in-out infinite 0.5s;
        }

        .animate-bounce-gentle-delay-2 {
            animation: bounceGentle 2s ease-in-out infinite 1s;
        }

        .animate-bounce-gentle-delay-3 {
            animation: bounceGentle 2s ease-in-out infinite 1.5s;
        }

        /* Services Section Scroll-Triggered Animations */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-60px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(60px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes iconPulse {

            0%,
            100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.1);
            }
        }

        /* Initial state - hidden */
        .services-animate {
            opacity: 0;
            transform: translateY(30px);
        }

        /* Triggered animation classes */
        .services-animate.animate-slide-in-left {
            animation: slideInLeft 1s ease-out forwards;
        }

        .services-animate.animate-slide-in-right {
            animation: slideInRight 1s ease-out forwards;
        }

        .services-animate.animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .services-animate.animate-service-item {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .services-animate.animate-icon-pulse {
            opacity: 1;
            transform: translateY(0);
            animation: iconPulse 2s ease-in-out infinite;
        }

        /* Book a Taxi Section Scroll-Triggered Animations */
        @keyframes tickBounce {
            0% {
                transform: scale(0);
                opacity: 0;
            }

            50% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes buttonPop {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes imageZoom {
            0% {
                transform: scale(0.9);
                opacity: 0;
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Initial state - hidden */
        .taxi-animate {
            opacity: 0;
            transform: translateY(30px);
        }

        /* Triggered animation classes */
        .taxi-animate.animate-slide-in-left {
            animation: slideInLeft 1s ease-out forwards;
        }

        .taxi-animate.animate-slide-in-right {
            animation: slideInRight 1s ease-out forwards;
        }

        .taxi-animate.animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .taxi-animate.animate-feature-item {
            animation: fadeInUp 0.6s ease-out forwards;
        }

        .taxi-animate.animate-tick-bounce {
            opacity: 0;
            transform: scale(0);
            animation: tickBounce 0.6s ease-out forwards;
        }

        .taxi-animate.animate-button-pop {
            animation: buttonPop 0.8s ease-out forwards;
        }

        .taxi-animate.animate-image-zoom {
            animation: imageZoom 1s ease-out forwards;
        }

        /* Testimonials Carousel Styles */
        .testimonials-carousel {
            position: relative;
            overflow: hidden;
        }

        .testimonials-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }

        .testimonial-slide {
            flex: 0 0 auto;
            width: 100%; /* Default: 1 testimonial on mobile */
            min-height: 200px;
        }

        /* Tablet: Show 2 testimonials at a time */
        @media (min-width: 768px) {
            .testimonial-slide {
                width: 50%; /* 2 testimonials = 50% each */
            }
        }

        /* Desktop: Show 3 testimonials at a time */
        @media (min-width: 1024px) {
            .testimonial-slide {
                width: 33.333333%; /* 3 testimonials = 33.333% each */
            }
        }

        /* Active dot styling */
        .carousel-dot.active {
            background-color: #fb923c;
            /* orange-400 */
        }

        /* Carousel navigation buttons */
        .carousel-prev:hover,
        .carousel-next:hover {
            background-color: #f9fafb;
            transform: translateY(-50%) scale(1.05);
        }

        /* Smooth carousel animations */
        .testimonials-track {
            will-change: transform;
        }

        /* Button hover animations */
        .transform {
            transition: transform 0.3s ease;
        }

        .hover\:scale-105:hover {
            transform: scale(1.05);
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <img class="h-14 w-auto" src="assets/images/logo_ark.svg" alt="ARK Logo">
                </div>

                <!-- Navigation Menu -->
                <nav class="hidden md:flex space-x-8">
                    <a href="#hero-section" class="nav-link text-orange-400 hover:text-orange-500 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-orange-400">
                        Home
                    </a>
                    <a href="#featured-vehicles-section" class="nav-link text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Car Rental
                    </a>
                    <a href="#services-section" class="nav-link text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Services
                    </a>
                    <a href="#book-taxi-section" class="nav-link text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Taxi
                    </a>
                    <a href="#contact-section" class="nav-link text-sky-950 hover:text-orange-400 px-3 py-2 text-lg font-medium transition-colors duration-200 border-b-2 border-transparent hover:border-orange-400">
                        Contact Us
                    </a>
                </nav>

                <!-- Language Select -->
                <div class="relative">
                    <button class="bg-orange-400 hover:bg-orange-500 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-300 cursor-pointer flex items-center" id="language-button">
                        <span id="selected-language">EN</span>
                        <svg class="ml-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <ul class="absolute right-0 mt-2 w-16 bg-white border border-gray-200 rounded-md shadow-lg z-10 hidden" id="language-dropdown">
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="en">EN</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="fr">FR</a>
                        </li>
                        <li>
                            <a href="#" class="block px-4 py-2 text-sm text-sky-950 hover:bg-orange-50 hover:text-orange-400 transition-colors duration-200" data-lang="de">DE</a>
                        </li>
                    </ul>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="text-sky-950 hover:text-orange-400 focus:outline-none focus:text-orange-400" id="mobile-menu-button">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="#hero-section" class="nav-link text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-orange-400">Home</a>
                <a href="#featured-vehicles-section" class="nav-link text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Car Rental</a>
                <a href="#services-section" class="nav-link text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Services</a>
                <a href="#book-taxi-section" class="nav-link text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Taxi</a>
                <a href="#contact-section" class="nav-link text-sky-950 hover:text-orange-400 block px-3 py-2 text-lg font-medium border-l-4 border-transparent hover:border-orange-400">Contact Us</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero-section" class="bg-gray-50 py-16 lg:py-20" style="background-color: #F9FAFB;">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                <!-- Left Content -->
                <div class="text-left order-2 lg:order-1 animate-fade-in-left">
                    <h1 class="text-xl lg:text-4xl xl:text-5xl font-bold text-sky-950 leading-tight mb-6 animate-slide-up">
                        Your Journey<br>
                        Your Choice
                    </h1>
                    <p class="text-base lg:text-lg text-gray-600 mb-8 leading-relaxed animate-slide-up-delay-1">
                        Rent a Car or Book a Taxi - All in One Place
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4 animate-slide-up-delay-2">
                        <button class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg text-base font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform">
                            Rent a Car
                        </button>
                        <button class="bg-sky-950 hover:bg-sky-900 text-white px-6 py-3 rounded-lg text-base font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform">
                            Book a Taxi
                        </button>
                    </div>
                </div>

                <!-- Right Content - Car Image -->
                <div class="flex justify-center lg:justify-end order-1 lg:order-2 animate-fade-in-right">
                    <img src="assets/images/hero_car.png" alt="Car Rental" class="w-full max-w-lg lg:max-w-xl xl:max-w-2xl h-auto object-contain animate-float">
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="bg-white py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12 animate-fade-in-up">
                <h2 class="text-3xl lg:text-4xl font-bold text-orange-400 mb-4">
                    Why Choose Us
                </h2>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
                <!-- 24/7 Customer Support -->
                <div class="text-center animate-fade-in-up-delay-1 hover:scale-105 transition-transform duration-300">
                    <div class="flex justify-center mb-6 animate-bounce-gentle">
                        <img src="assets/images/headphone.svg" alt="24/7 Customer Support" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        24/7 Customer Support
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Always here to help
                    </p>
                </div>

                <!-- No Hidden Charges -->
                <div class="text-center animate-fade-in-up-delay-2 hover:scale-105 transition-transform duration-300">
                    <div class="flex justify-center mb-6 animate-bounce-gentle-delay-1">
                        <img src="assets/images/shield.svg" alt="No Hidden Charges" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        No Hidden Charges
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Transparent pricing
                    </p>
                </div>

                <!-- Secure Payments -->
                <div class="text-center animate-fade-in-up-delay-3 hover:scale-105 transition-transform duration-300">
                    <div class="flex justify-center mb-6 animate-bounce-gentle-delay-2">
                        <img src="assets/images/card.svg" alt="Secure Payments" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        Secure Payments
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Safe & encrypted
                    </p>
                </div>

                <!-- Free Cancellation -->
                <div class="text-center animate-fade-in-up-delay-4 hover:scale-105 transition-transform duration-300">
                    <div class="flex justify-center mb-6 animate-bounce-gentle-delay-3">
                        <img src="assets/images/customer.svg" alt="Free Cancellation" class="w-16 h-16">
                    </div>
                    <h3 class="text-xl font-bold text-orange-400 mb-3">
                        Free Cancellation
                    </h3>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Flexible booking
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Featured Vehicles Section -->
    <section id="featured-vehicles-section" class="bg-gray-50 py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-4">
                    Our Featured Vehicles
                </h2>
            </div>

            <!-- Vehicles Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Mercedes-Benz E-Class -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_white.jpg" alt="Mercedes-Benz E-Class" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">Perodua Bezza White</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$89</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Hybrid" class="w-4 h-4">
                                <span>Hybrid</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>

                <!-- BMW 5 Series -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_brown.jpg" alt="BMW 5 Series" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">BMW 5 Series</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$79</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Petrol" class="w-4 h-4">
                                <span>Petrol</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>

                <!-- Tesla Model 3 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div class="aspect-w-16 aspect-h-10 bg-gray-100">
                        <img src="assets/images/car_silver.jpg" alt="Tesla Model 3" class="w-full h-48 object-cover">
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold text-sky-950">Tesla Model 3</h3>
                            <div class="text-right">
                                <span class="text-2xl font-bold text-sky-950">$99</span>
                                <span class="text-orange-400 text-sm">/day</span>
                            </div>
                        </div>
                        <p class="text-orange-400 text-sm font-medium mb-4">Luxury</p>

                        <!-- Features -->
                        <div class="flex flex-wrap gap-4 mb-6 text-sm text-gray-600">
                            <div class="flex items-center gap-2">
                                <img src="assets/images/seats.svg" alt="Seats" class="w-4 h-4">
                                <span>5 Seats</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/automatic.svg" alt="Automatic" class="w-4 h-4">
                                <span>Automatic</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <img src="assets/images/petrol.svg" alt="Electric" class="w-4 h-4">
                                <span>Electric</span>
                            </div>
                        </div>

                        <button class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Book Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="bg-white py-16 lg:py-20" id="services-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">

                <!-- Car Rental Services -->
                <div class="services-animate" data-animation="slide-in-left">
                    <h2 class="text-2xl lg:text-3xl font-bold text-sky-950 mb-8 services-animate" data-animation="fade-in-up">
                        Car Rental Services
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Wide Vehicle Selection -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="400">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="2000">
                                <img src="assets/images/wide.svg" alt="Wide Vehicle Selection" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Wide Vehicle Selection</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- 24/7 Customer Support -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="600">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="2200">
                                <img src="assets/images/24_7_customer.svg" alt="24/7 Customer Support" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">24/7 Customer Support</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Flexible Pickup/Drop-off -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="800">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="2400">
                                <img src="assets/images/flexible.svg" alt="Flexible Pickup/Drop-off" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Flexible Pickup/Drop-off</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Insurance Included -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="1000">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="2600">
                                <img src="assets/images/insurance.svg" alt="Insurance Included" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Insurance Included</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Taxi Services -->
                <div class="services-animate" data-animation="slide-in-right" data-delay="300">
                    <h2 class="text-2xl lg:text-3xl font-bold text-sky-950 mb-8 services-animate" data-animation="fade-in-up" data-delay="200">
                        Taxi Services
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Professional Drivers -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="1200">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="2800">
                                <img src="assets/images/professional.svg" alt="Professional Drivers" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Professional Drivers</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Airport Transfers -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="1400">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="3000">
                                <img src="assets/images/airport.svg" alt="Airport Transfers" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Airport Transfers</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Fixed Rates -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="1600">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="3200">
                                <img src="assets/images/fix_rate.svg" alt="Fixed Rates" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Fixed Rates</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>

                        <!-- Corporate Accounts -->
                        <div class="flex items-start gap-4 services-animate hover:bg-gray-50 p-4 rounded-lg transition-all duration-300 hover:shadow-md" data-animation="service-item" data-delay="1800">
                            <div class="flex-shrink-0 mt-1 services-animate" data-animation="icon-pulse" data-delay="3400">
                                <img src="assets/images/corporate.svg" alt="Corporate Accounts" class="w-5 h-5">
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-sky-950 mb-2">Corporate Accounts</h3>
                                <p class="text-gray-600 text-sm leading-relaxed">
                                    Professional and reliable service for your needs
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Book a Taxi Section -->
    <section class="bg-gray-50 py-16 lg:py-20" id="book-taxi-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">

                <!-- Left Content -->
                <div class="order-2 lg:order-1 taxi-animate" data-animation="slide-in-left">
                    <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-6 taxi-animate" data-animation="fade-in-up" data-delay="200">
                        Book a Taxi
                    </h2>

                    <p class="text-gray-600 text-lg mb-8 leading-relaxed taxi-animate" data-animation="fade-in-up" data-delay="400">
                        Experience reliable and comfortable taxi services with our professional drivers.
                        Book your ride today and enjoy hassle-free transportation.
                    </p>

                    <!-- Features List -->
                    <div class="space-y-4 mb-8">
                        <div class="flex items-center gap-3 taxi-animate" data-animation="feature-item" data-delay="600">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0 taxi-animate" data-animation="tick-bounce" data-delay="800">
                            <span class="text-gray-700">Professional and experienced drivers</span>
                        </div>
                        <div class="flex items-center gap-3 taxi-animate" data-animation="feature-item" data-delay="700">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0 taxi-animate" data-animation="tick-bounce" data-delay="900">
                            <span class="text-gray-700">24/7 availability for your convenience</span>
                        </div>
                        <div class="flex items-center gap-3 taxi-animate" data-animation="feature-item" data-delay="800">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0 taxi-animate" data-animation="tick-bounce" data-delay="1000">
                            <span class="text-gray-700">Competitive and transparent pricing</span>
                        </div>
                        <div class="flex items-center gap-3 taxi-animate" data-animation="feature-item" data-delay="900">
                            <img src="assets/images/tick.svg" alt="Check" class="w-5 h-5 flex-shrink-0 taxi-animate" data-animation="tick-bounce" data-delay="1100">
                            <span class="text-gray-700">Clean and well-maintained vehicles</span>
                        </div>
                    </div>

                    <!-- CTA Button -->
                    <button class="bg-orange-400 hover:bg-orange-500 text-white px-6 py-3 rounded-lg text-base font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 transform taxi-animate" data-animation="button-pop" data-delay="1000">
                        Book Now
                    </button>

                </div>

                <!-- Right Image -->
                <div class="order-1 lg:order-2 taxi-animate" data-animation="slide-in-right" data-delay="300">
                    <div class="relative">
                        <img src="assets/images/taxi_car.png" alt="Taxi Car" class="w-full h-auto rounded-lg taxi-animate" data-animation="image-zoom" data-delay="500">
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- What Our Customers Say Section -->
    <section class="bg-white py-16 lg:py-20" id="testimonials-section">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Section Title -->
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-4">
                    What Our Customers Say
                </h2>
            </div>

            <!-- Testimonials Carousel -->
            <div class="relative">
                <!-- Carousel Container -->
                <div class="testimonials-carousel overflow-hidden">
                    <div class="testimonials-track flex transition-transform duration-500 ease-in-out">

                        <!-- Testimonial 1 - Sarah Johnson -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        SJ
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">Sarah Johnson</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Excellent service! The car was in perfect condition and the staff was very helpful. I'll definitely be using ARK for all my future transportation needs."
                                </p>
                            </div>
                        </div>

                        <!-- Testimonial 2 - Michael Chen -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        MC
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">Michael Chen</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Very professional and reliable service. The booking process was smooth and the driver was punctual. Will definitely use their services again."
                                </p>
                            </div>
                        </div>

                        <!-- Testimonial 3 - Emily Davis -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        ED
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">Emily Davis</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Great experience from booking to drop-off. The vehicle was clean and comfortable. Highly recommended for anyone needing reliable transportation!"
                                </p>
                            </div>
                        </div>

                        <!-- Testimonial 4 - David Wilson -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        DW
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">David Wilson</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Outstanding customer service and competitive pricing. The taxi arrived exactly on time and the driver was courteous and professional throughout the journey."
                                </p>
                            </div>
                        </div>

                        <!-- Testimonial 5 - Lisa Thompson -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        LT
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">Lisa Thompson</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Perfect for airport transfers! The driver tracked my flight and was waiting when I arrived. The car was spotless and the ride was smooth and comfortable."
                                </p>
                            </div>
                        </div>

                        <!-- Testimonial 6 - James Rodriguez -->
                        <div class="testimonial-slide px-4">
                            <div class="bg-gray-50 rounded-lg p-6 shadow-sm h-full">
                                <!-- Customer Info -->
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                        JR
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-sky-950">James Rodriguez</h3>
                                        <!-- Star Rating -->
                                        <div class="flex gap-1 mt-1">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                            <img src="assets/images/star.svg" alt="Star" class="w-4 h-4">
                                        </div>
                                    </div>
                                </div>
                                <!-- Testimonial Text -->
                                <p class="text-gray-600 leading-relaxed">
                                    "Exceptional value for money! The rental process was hassle-free and the vehicle exceeded my expectations. ARK has become my go-to transportation service."
                                </p>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- Navigation Dots -->
                <div class="flex justify-center mt-8 space-x-2">
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="0"></button>
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="1"></button>
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="2"></button>
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="3"></button>
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="4"></button>
                    <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-orange-400 transition-colors duration-200" data-slide="5"></button>
                </div>

                <!-- Navigation Arrows (Optional - Hidden on Mobile) -->
                <button class="carousel-prev absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200 hidden lg:block">
                    <svg class="w-6 h-6 text-sky-950" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button class="carousel-next absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-all duration-200 hidden lg:block">
                    <svg class="w-6 h-6 text-sky-950" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Contact Us Section -->
    <section id="contact-section" class="bg-gray-50 py-16 lg:py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">

                <!-- Left Side - Contact Form -->
                <div>
                    <h2 class="text-3xl lg:text-4xl font-bold text-sky-950 mb-8">
                        Contact Us
                    </h2>

                    <!-- Contact Form -->
                    <form class="space-y-6">
                        <!-- Name Field -->
                        <div>
                            <label for="name" class="block text-orange-400 font-medium mb-2">Name</label>
                            <input type="text" id="name" name="name" placeholder="Your name" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors">
                        </div>

                        <!-- Email Field -->
                        <div>
                            <label for="email" class="block text-orange-400 font-medium mb-2">Email</label>
                            <input type="email" id="email" name="email" placeholder="Your email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors">
                        </div>

                        <!-- Message Field -->
                        <div>
                            <label for="message" class="block text-orange-400 font-medium mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" placeholder="Your message" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-400 focus:border-orange-400 outline-none transition-colors resize-vertical"></textarea>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="w-full bg-sky-950 hover:bg-sky-900 text-white py-3 rounded-lg font-semibold transition-colors duration-200">
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Right Side - Contact Information -->
                <div>
                    <h3 class="text-2xl font-bold text-sky-950 mb-8">
                        Contact Information
                    </h3>

                    <!-- Contact Details -->
                    <div class="space-y-6 mb-8">
                        <!-- Address -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_pin.svg" alt="Location" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">123 Transportation Ave, City, Country</span>
                        </div>

                        <!-- Phone -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_phone.svg" alt="Phone" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">****** 567 890</span>
                        </div>

                        <!-- Email -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_email.svg" alt="Email" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700"><EMAIL></span>
                        </div>

                        <!-- Hours -->
                        <div class="flex items-start gap-4">
                            <img src="assets/images/contact_time.png" alt="Hours" class="w-5 h-5 mt-1 flex-shrink-0">
                            <span class="text-gray-700">24/7 Available</span>
                        </div>
                    </div>

                    <!-- Map -->
                    <div class="rounded-lg overflow-hidden">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.1234567890123!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c25a316e5b7c5d%3A0x1234567890abcdef!2s123%20Transportation%20Ave%2C%20New%20York%2C%20NY%2010001%2C%20USA!5e0!3m2!1sen!2sus!4v1234567890123!5m2!1sen!2sus" width="100%" height="256" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" class="w-full h-64"></iframe>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-12 border-t border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

                <!-- Company Info -->
                <div class="lg:col-span-1">
                    <div class="flex items-center gap-3 mb-4">
                        <img src="assets/images/logo_ark.svg" alt="ARK Logo" class="h-14 w-auto">
                    </div>
                    <p class="text-gray-600 text-sm leading-relaxed">
                        Your trusted partner in transportation services.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">About Us</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Our Fleet</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Taxi Service</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-sky-950 transition-colors text-sm">Contact</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Contact</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3">
                            <img src="assets/images/footer_phone.svg" alt="Phone" class="w-4 h-4 flex-shrink-0">
                            <span class="text-gray-600 text-sm">****** 567 890</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <img src="assets/images/footer_email.png" alt="Email" class="w-4 h-4 flex-shrink-0">
                            <span class="text-gray-600 text-sm"><EMAIL></span>
                        </div>
                    </div>
                </div>

                <!-- Follow Us -->
                <div>
                    <h3 class="text-orange-400 font-semibold mb-4">Follow Us</h3>
                    <div class="flex gap-3">
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/facebook.svg" alt="Facebook" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/twitter.svg" alt="Twitter" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/instamgram.svg" alt="Instagram" class="w-5 h-5">
                        </a>
                        <a href="#" class="w-10 h-10 bg-sky-950 rounded-full flex items-center justify-center hover:bg-sky-900 transition-colors">
                            <img src="assets/images/linkedin.svg" alt="LinkedIn" class="w-5 h-5">
                        </a>
                    </div>
                </div>

            </div>

            <!-- Footer Bottom -->
            <div class="border-t border-gray-200 mt-8 pt-6">
                <div class="text-center">
                    <p class="text-sky-950 text-sm">
                        © 2024 Ark Car Rental. All rights reserved.
                    </p>
                </div>
            </div>
        </div>
    </footer>



    <!-- jQuery for mobile menu toggle, language dropdown, and animations -->
    <script>
        $(document).ready(function () {
            // Smooth scrolling for navigation links
            $('.nav-link').on('click', function(e) {
                e.preventDefault();

                const targetId = $(this).attr('href');
                const $targetSection = $(targetId);

                if ($targetSection.length) {
                    // Close mobile menu if open
                    $('#mobile-menu').addClass('hidden');

                    // Calculate offset for fixed header
                    const headerHeight = $('header').outerHeight();
                    const targetOffset = $targetSection.offset().top - headerHeight - 20; // 20px extra padding

                    // Smooth scroll to target section
                    $('html, body').animate({
                        scrollTop: targetOffset
                    }, 800, 'swing'); // 800ms duration with swing easing

                    // Update active navigation state
                    updateActiveNavigation(targetId);
                }
            });

            // Update active navigation based on current section
            function updateActiveNavigation(activeSection) {
                // Remove active state from all nav links
                $('.nav-link').removeClass('text-orange-400 border-orange-400')
                             .addClass('text-sky-950 border-transparent');

                // Add active state to current section link
                $(`.nav-link[href="${activeSection}"]`)
                    .removeClass('text-sky-950 border-transparent')
                    .addClass('text-orange-400 border-orange-400');
            }

            // Mobile menu toggle
            $('#mobile-menu-button').on('click', function () {
                $('#mobile-menu').toggleClass('hidden');
            });

            // Language dropdown functionality
            const $languageButton = $('#language-button');
            const $languageDropdown = $('#language-dropdown');
            const $selectedLanguage = $('#selected-language');

            // Toggle dropdown
            $languageButton.on('click', function (e) {
                e.preventDefault();
                $languageDropdown.toggleClass('hidden');
            });

            // Handle language selection
            $languageDropdown.find('a[data-lang]').on('click', function (e) {
                e.preventDefault();
                const selectedLang = $(this).data('lang').toUpperCase();
                $selectedLanguage.text(selectedLang);
                $languageDropdown.addClass('hidden');

                // You can add your language switching logic here
                console.log('Language changed to:', selectedLang);
            });

            // Close dropdown when clicking outside
            $(document).on('click', function (e) {
                if (!$languageButton.is(e.target) &&
                    !$languageButton.has(e.target).length &&
                    !$languageDropdown.is(e.target) &&
                    !$languageDropdown.has(e.target).length) {
                    $languageDropdown.addClass('hidden');
                }
            });

            // Services Section Scroll-Triggered Animations using jQuery
            function triggerServicesAnimations() {
                const $servicesSection = $('#services-section');
                const $animateElements = $servicesSection.find('.services-animate');

                $animateElements.each(function () {
                    const $element = $(this);
                    const animationType = $element.data('animation');
                    const delay = $element.data('delay') || 0;

                    setTimeout(function () {
                        switch (animationType) {
                            case 'slide-in-left':
                                $element.addClass('animate-slide-in-left');
                                break;
                            case 'slide-in-right':
                                $element.addClass('animate-slide-in-right');
                                break;
                            case 'fade-in-up':
                                $element.addClass('animate-fade-in-up');
                                break;
                            case 'service-item':
                                $element.addClass('animate-service-item');
                                break;
                            case 'icon-pulse':
                                $element.addClass('animate-icon-pulse');
                                break;
                        }
                    }, parseInt(delay));
                });
            }

            // Book a Taxi Section Scroll-Triggered Animations using jQuery
            function triggerTaxiAnimations() {
                const $taxiSection = $('#book-taxi-section');
                const $animateElements = $taxiSection.find('.taxi-animate');

                $animateElements.each(function () {
                    const $element = $(this);
                    const animationType = $element.data('animation');
                    const delay = $element.data('delay') || 0;

                    setTimeout(function () {
                        switch (animationType) {
                            case 'slide-in-left':
                                $element.addClass('animate-slide-in-left');
                                break;
                            case 'slide-in-right':
                                $element.addClass('animate-slide-in-right');
                                break;
                            case 'fade-in-up':
                                $element.addClass('animate-fade-in-up');
                                break;
                            case 'feature-item':
                                $element.addClass('animate-feature-item');
                                break;
                            case 'tick-bounce':
                                $element.addClass('animate-tick-bounce');
                                break;
                            case 'button-pop':
                                $element.addClass('animate-button-pop');
                                break;
                            case 'image-zoom':
                                $element.addClass('animate-image-zoom');
                                break;
                        }
                    }, parseInt(delay));
                });
            }

            // Intersection Observer for Services Section
            if ('IntersectionObserver' in window) {
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const servicesObserver = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            triggerServicesAnimations();
                            // Unobserve after animation is triggered
                            servicesObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                // Intersection Observer for Book a Taxi Section
                const taxiObserver = new IntersectionObserver(function (entries) {
                    entries.forEach(function (entry) {
                        if (entry.isIntersecting) {
                            triggerTaxiAnimations();
                            // Unobserve after animation is triggered
                            taxiObserver.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                // Start observing both sections
                const servicesSection = document.getElementById('services-section');
                const taxiSection = document.getElementById('book-taxi-section');

                if (servicesSection) {
                    servicesObserver.observe(servicesSection);
                }

                if (taxiSection) {
                    taxiObserver.observe(taxiSection);
                }
            } else {
                // Fallback for browsers without Intersection Observer
                let servicesTriggered = false;
                let taxiTriggered = false;

                $(window).on('scroll', function () {
                    const scrollTop = $(window).scrollTop();
                    const windowHeight = $(window).height();

                    // Check Services Section
                    if (!servicesTriggered) {
                        const $servicesSection = $('#services-section');
                        if ($servicesSection.length) {
                            const sectionTop = $servicesSection.offset().top;
                            const sectionHeight = $servicesSection.outerHeight();

                            if (scrollTop + windowHeight > sectionTop + (sectionHeight * 0.1)) {
                                triggerServicesAnimations();
                                servicesTriggered = true;
                            }
                        }
                    }

                    // Check Book a Taxi Section
                    if (!taxiTriggered) {
                        const $taxiSection = $('#book-taxi-section');
                        if ($taxiSection.length) {
                            const sectionTop = $taxiSection.offset().top;
                            const sectionHeight = $taxiSection.outerHeight();

                            if (scrollTop + windowHeight > sectionTop + (sectionHeight * 0.1)) {
                                triggerTaxiAnimations();
                                taxiTriggered = true;
                            }
                        }
                    }

                    // Remove scroll listener when both sections are triggered
                    if (servicesTriggered && taxiTriggered) {
                        $(window).off('scroll');
                    }
                });
            }

            // Testimonials Carousel Functionality
            let currentSlide = 0;
            let autoSlideInterval;
            const $track = $('.testimonials-track');
            const $slides = $('.testimonial-slide');
            const $dots = $('.carousel-dot');

            // Get number of slides to show based on screen size
            function getSlidesPerView() {
                if ($(window).width() >= 1024) return 3; // lg: 3 slides
                if ($(window).width() >= 768) return 2;  // md: 2 slides
                return 1; // sm: 1 slide
            }

            // Get total number of slide positions
            function getTotalSlides() {
                const slidesPerView = getSlidesPerView();
                const totalTestimonials = $slides.length; // 6 testimonials

                // Calculate how many positions we can slide to
                // For 6 slides: Mobile(1 per view) = 6 positions, Tablet(2 per view) = 5 positions, Desktop(3 per view) = 4 positions
                return Math.max(1, totalTestimonials - slidesPerView + 1);
            }

            // Update carousel position
            function updateCarousel() {
                const slidesPerView = getSlidesPerView();

                // Calculate movement based on current screen size
                let slideWidth;
                if (slidesPerView === 1) {
                    // Mobile: Each slide is 100% wide, move by 100%
                    slideWidth = 100;
                } else if (slidesPerView === 2) {
                    // Tablet: Each slide is 50% wide, move by 50%
                    slideWidth = 50;
                } else {
                    // Desktop: Each slide is 33.333% wide, move by 33.333%
                    slideWidth = 33.333333;
                }

                const translateX = -(currentSlide * slideWidth);
                $track.css('transform', `translateX(${translateX}%)`);

                // Update dots - show dot for each possible position
                $dots.removeClass('active');
                if (currentSlide < $dots.length) {
                    $dots.eq(currentSlide).addClass('active');
                }
            }

            // Go to specific slide
            function goToSlide(slideIndex) {
                const totalSlides = getTotalSlides();
                if (slideIndex >= 0 && slideIndex < totalSlides) {
                    currentSlide = slideIndex;
                    updateCarousel();
                    resetAutoSlide();
                }
            }

            // Next slide
            function nextSlide() {
                const totalSlides = getTotalSlides();
                currentSlide = (currentSlide + 1) % totalSlides;
                updateCarousel();
            }

            // Previous slide
            function prevSlide() {
                const totalSlides = getTotalSlides();
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                updateCarousel();
            }

            // Auto slide functionality
            function startAutoSlide() {
                autoSlideInterval = setInterval(nextSlide, 4000); // Change slide every 4 seconds
            }

            function stopAutoSlide() {
                clearInterval(autoSlideInterval);
            }

            function resetAutoSlide() {
                stopAutoSlide();
                startAutoSlide();
            }

            // Event listeners
            $('.carousel-dot').on('click', function() {
                const slideIndex = $(this).data('slide');
                goToSlide(slideIndex);
            });

            $('.carousel-prev').on('click', function() {
                prevSlide();
                resetAutoSlide();
            });

            $('.carousel-next').on('click', function() {
                nextSlide();
                resetAutoSlide();
            });

            // Pause auto-slide on hover
            $('#testimonials-section').on('mouseenter', stopAutoSlide);
            $('#testimonials-section').on('mouseleave', startAutoSlide);

            // Handle window resize
            $(window).on('resize', function() {
                // Recalculate slides and reset position if needed
                const newTotalSlides = getTotalSlides();
                if (currentSlide >= newTotalSlides) {
                    currentSlide = Math.max(0, newTotalSlides - 1);
                }
                updateCarousel();
                updateDots();
            });

            // Update dots based on current screen size
            function updateDots() {
                const totalSlides = getTotalSlides();
                const slidesPerView = getSlidesPerView();

                // Hide all dots first
                $dots.hide();

                // Show appropriate number of dots based on screen size and content
                if (slidesPerView === 1) {
                    // Mobile: Show all 6 dots (one for each testimonial)
                    $dots.slice(0, 6).show();
                } else if (slidesPerView === 2) {
                    // Tablet: Show 5 dots (6 testimonials - 2 per view + 1)
                    $dots.slice(0, 5).show();
                } else {
                    // Desktop: Show 4 dots (6 testimonials - 3 per view + 1)
                    $dots.slice(0, 4).show();
                }
            }

            // Initialize carousel
            updateDots();
            updateCarousel();
            startAutoSlide();

            // Touch/swipe support for mobile
            let startX = 0;
            let isDragging = false;

            $track.on('touchstart mousedown', function(e) {
                isDragging = true;
                startX = e.type === 'touchstart' ? e.originalEvent.touches[0].clientX : e.clientX;
                stopAutoSlide();
            });

            $(document).on('touchmove mousemove', function(e) {
                if (!isDragging) return;
                e.preventDefault();

                const currentX = e.type === 'touchmove' ? e.originalEvent.touches[0].clientX : e.clientX;
                const diffX = startX - currentX;

                // Add visual feedback during drag
                const dragDistance = diffX / $(window).width() * 100;
                const currentTransform = -(currentSlide * (100 / getSlidesPerView()));
                $track.css('transform', `translateX(${currentTransform - dragDistance}%)`);
            });

            $(document).on('touchend mouseup', function(e) {
                if (!isDragging) return;
                isDragging = false;

                const endX = e.type === 'touchend' ? e.originalEvent.changedTouches[0].clientX : e.clientX;
                const diffX = startX - endX;
                const threshold = 50; // Minimum swipe distance

                if (Math.abs(diffX) > threshold) {
                    if (diffX > 0) {
                        nextSlide(); // Swipe left - next slide
                    } else {
                        prevSlide(); // Swipe right - previous slide
                    }
                } else {
                    updateCarousel(); // Snap back to current slide
                }

                resetAutoSlide();
            });
        });
    </script>
</body>

</html>